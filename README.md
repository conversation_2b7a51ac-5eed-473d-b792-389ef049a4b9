# 🚀 SmartV IPTV - Versão PHP Completa

## 📋 Descrição

Esta é uma versão completa em PHP do sistema SmartV IPTV, desenvolvida com design idêntico ao projeto original React/Node.js. Inclui todas as funcionalidades principais:

- ✅ **Interface moderna e responsiva** com Tailwind CSS
- ✅ **Sistema de temas** (claro/escuro)
- ✅ **Teste grátis real** usando API do IPTV
- ✅ **Pagamento PIX** integrado com Mercado Pago
- ✅ **Design idêntico** ao projeto original
- ✅ **Totalmente funcional** sem dependências de Node.js

## 🎨 Características Visuais

### Cores e Tema
- **Esquema de cores** idêntico ao original usando CSS Variables
- **Modo escuro/claro** com transição suave
- **Gradientes** e animações preservados
- **Ícones Lucide** para consistência visual

### Layout Responsivo
- **Mobile-first** design
- **Grid system** adaptativo
- **Cards com hover effects**
- **Animações** e transições suaves

## 🔧 Funcionalidades

### 1. Teste Grátis (4 horas)
- **API Real**: Integração com `https://pop.sigma.vin/api/chatbot/e6WnZE7WK8/rlKWO3Wzo7`
- **Parsing completo** de credenciais, DNS, links M3U/HLS
- **Exibição organizada** dos dados de acesso
- **Fallback local** em caso de falha da API

### 2. Sistema de Pagamento PIX
- **Mercado Pago** integração completa
- **Validação CPF/CNPJ** real
- **QR Code** gerado automaticamente
- **Cópia do código PIX** com um clique

### 3. Planos de Assinatura
- **Básico**: R$ 19,90 - 5.000+ canais
- **Premium**: R$ 29,90 - 10.000+ canais (Mais Popular)
- **Ultra**: R$ 39,90 - 15.000+ canais 4K

## 📁 Estrutura do Projeto

```
smartv-php/
├── index.php              # Página principal
├── api/
│   ├── test.php          # API para teste grátis
│   └── payment.php       # API para pagamento PIX
└── README.md             # Este arquivo
```

## 🚀 Instalação e Configuração

### Pré-requisitos
- **PHP 7.4+** com extensões:
  - `curl` ou `file_get_contents` habilitado
  - `json` extension
  - `preg` functions (regex)

### Instalação Simples

1. **Upload dos arquivos**:
   ```bash
   # Copie todos os arquivos para seu servidor web
   cp -r smartv-php/* /var/www/html/smartv/
   ```

2. **Configurar permissões**:
   ```bash
   chmod 755 /var/www/html/smartv/
   chmod 644 /var/www/html/smartv/*.php
   chmod 644 /var/www/html/smartv/api/*.php
   ```

3. **Configurar servidor web** (Apache/Nginx):
   - Apontar domínio para a pasta do projeto
   - Habilitar rewrite rules se necessário

### Configuração para smartv.shop

Para usar no domínio `smartv.shop`, simplesmente:

1. **Upload para `/www/wwwroot/smarttv/`**
2. **Configurar Nginx** (já fornecido)
3. **Testar acesso**: `https://smartv.shop`

## 🔐 Configurações da API

### API de Teste IPTV
- **URL**: `https://pop.sigma.vin/api/chatbot/e6WnZE7WK8/rlKWO3Wzo7`
- **Método**: POST
- **Headers**: `Content-Type: application/json`

### Mercado Pago
- **Token de teste** já configurado
- **Para produção**: Substitua o token em `api/payment.php`
- **Webhook**: Configure em `https://smartv.shop/webhook/mercadopago`

## 🎯 Funcionalidades Implementadas

### ✅ Interface Completa
- [x] Navigation bar responsiva
- [x] Hero section com gradiente
- [x] Seção de características
- [x] Formulário de teste grátis
- [x] Planos de assinatura
- [x] Formulário de contato
- [x] Footer completo

### ✅ Funcionalidades JavaScript
- [x] Toggle de tema (claro/escuro)
- [x] Smooth scrolling
- [x] Formulário de teste com validação
- [x] Integração com SweetAlert2
- [x] Formulário de pagamento PIX
- [x] Cópia de código PIX
- [x] Loading states e animações

### ✅ APIs Backend
- [x] API de teste (`/api/test.php`)
- [x] API de pagamento (`/api/payment.php`)
- [x] Parsing completo de respostas IPTV
- [x] Validação de CPF/CNPJ
- [x] Integração Mercado Pago

## 🔧 Personalização

### Alterar Cores
Edite as CSS variables no `<style>` do `index.php`:
```css
:root {
    --primary: oklch(0.205 0 0);  /* Cor primária */
    --background: oklch(1 0 0);   /* Fundo */
    /* ... outras cores */
}
```

### Alterar Planos
Modifique a seção de planos no HTML e a função `selectPlan()` no JavaScript.

### Configurar APIs
- **Teste**: Altere a URL em `api/test.php`
- **Pagamento**: Altere o token em `api/payment.php`

## 🐛 Troubleshooting

### Erro: "file_get_contents(): SSL operation failed"
```php
// Adicione no início dos arquivos API:
$context = stream_context_create([
    "ssl" => [
        "verify_peer" => false,
        "verify_peer_name" => false,
    ],
]);
```

### Erro: "Call to undefined function curl_init()"
- Instale a extensão cURL do PHP
- Ou use `file_get_contents()` (já implementado)

### CORS Issues
- Headers CORS já configurados
- Verifique se o servidor permite `Access-Control-Allow-Origin`

## 📱 Compatibilidade

### Navegadores Suportados
- ✅ Chrome 80+
- ✅ Firefox 75+
- ✅ Safari 13+
- ✅ Edge 80+

### Dispositivos
- ✅ Desktop (1920x1080+)
- ✅ Tablet (768x1024)
- ✅ Mobile (375x667+)

## 🚀 Performance

### Otimizações Implementadas
- **CSS inline** para carregamento rápido
- **CDN** para bibliotecas externas
- **Lazy loading** de ícones
- **Minificação** automática do Tailwind

### Métricas Esperadas
- **First Paint**: < 1s
- **Interactive**: < 2s
- **Lighthouse Score**: 90+

## 📞 Suporte

Para dúvidas ou problemas:
- **E-mail**: <EMAIL>
- **WhatsApp**: +55 (11) 99999-9999

## 📄 Licença

Este projeto é proprietário e confidencial. Todos os direitos reservados.

---

**Desenvolvido com ❤️ para SmartV IPTV**
