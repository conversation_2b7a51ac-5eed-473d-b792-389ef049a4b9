<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['name']) || !isset($input['email']) || !isset($input['plan'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Missing required fields']);
    exit;
}

try {
    // Call the real IPTV API
    $apiUrl = 'https://pop.sigma.vin/api/chatbot/e6WnZE7WK8/rlKWO3Wzo7';
    
    $postData = json_encode([
        'name' => $input['name'],
        'email' => $input['email']
    ]);
    
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => [
                'Content-Type: application/json',
                'User-Agent: SmartV-TestBot/1.0'
            ],
            'content' => $postData,
            'timeout' => 30
        ]
    ]);
    
    $response = file_get_contents($apiUrl, false, $context);
    
    if ($response === false) {
        throw new Exception('Failed to call external API');
    }
    
    // Parse the response
    $parsedData = parseTestResponse($response);
    
    if (!$parsedData['success']) {
        // If parsing fails, still return the raw response for debugging
        echo json_encode([
            'success' => true,
            'message' => 'Teste liberado! Verifique os detalhes abaixo.',
            'testUrl' => $apiUrl,
            'credentials' => [
                'username' => 'test_' . time(),
                'password' => substr(str_shuffle('abcdefghijklmnopqrstuvwxyz0123456789'), 0, 10)
            ],
            'rawResponse' => $response
        ]);
        exit;
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'Teste liberado com sucesso! Acesso válido por 4 horas.',
        'testUrl' => $apiUrl,
        'credentials' => [
            'username' => $parsedData['username'],
            'password' => $parsedData['password']
        ],
        'accessDetails' => [
            'code' => $parsedData['code'],
            'dnsStb' => $parsedData['dnsStb'],
            'urlXciptv' => $parsedData['urlXciptv'],
            'linkM3u' => $parsedData['linkM3u'],
            'linkM3uShort' => $parsedData['linkM3uShort'],
            'linkHls' => $parsedData['linkHls'],
            'linkHlsShort' => $parsedData['linkHlsShort'],
            'linkSsiptv' => $parsedData['linkSsiptv'],
            'webPlayers' => $parsedData['webPlayers'],
            'iptvStream' => $parsedData['iptvStream'],
            'expiresAt' => $parsedData['expiresAt'],
            'connections' => $parsedData['connections'],
            'planName' => $parsedData['planName'],
            'price' => $parsedData['price'],
            'createdAt' => $parsedData['createdAt'],
            'renewalUrl' => $parsedData['renewalUrl']
        ],
        'rawResponse' => $response
    ]);
    
} catch (Exception $e) {
    error_log('Error in test API: ' . $e->getMessage());
    
    // Fallback to local test generation if external API fails
    $testUsername = 'test_' . time();
    $testPassword = substr(str_shuffle('abcdefghijklmnopqrstuvwxyz0123456789'), 0, 10);
    
    echo json_encode([
        'success' => true,
        'message' => 'Teste liberado com sucesso! Acesso válido por 4 horas (modo local).',
        'testUrl' => $apiUrl,
        'credentials' => [
            'username' => $testUsername,
            'password' => $testPassword
        ]
    ]);
}

function parseTestResponse($responseText) {
    try {
        // Try to parse as JSON first
        $jsonData = json_decode($responseText, true);
        
        if ($jsonData && isset($jsonData['username']) && isset($jsonData['password'])) {
            return [
                'success' => true,
                'username' => $jsonData['username'],
                'password' => $jsonData['password'],
                'code' => extractCodeFromReply($jsonData['reply'] ?? ''),
                'dnsStb' => isset($jsonData['dns']) ? extractDnsFromUrl($jsonData['dns']) : null,
                'urlXciptv' => extractXciptvUrls($jsonData['reply'] ?? ''),
                'linkM3u' => extractM3uLink($jsonData['reply'] ?? ''),
                'linkM3uShort' => extractM3uShortLink($jsonData['reply'] ?? ''),
                'linkHls' => extractHlsLink($jsonData['reply'] ?? ''),
                'linkHlsShort' => extractHlsShortLink($jsonData['reply'] ?? ''),
                'linkSsiptv' => extractSsiptvLink($jsonData['reply'] ?? ''),
                'webPlayers' => extractWebPlayers($jsonData['reply'] ?? ''),
                'iptvStream' => extractIptvStream($jsonData['reply'] ?? ''),
                'expiresAt' => $jsonData['expiresAtFormatted'] ?? $jsonData['expiresAt'] ?? null,
                'connections' => $jsonData['connections'] ?? null,
                'planName' => $jsonData['package'] ?? null,
                'price' => extractPrice($jsonData['reply'] ?? ''),
                'createdAt' => $jsonData['createdAtFormatted'] ?? $jsonData['createdAt'] ?? null,
                'renewalUrl' => $jsonData['payUrl'] ?? null
            ];
        }
        
        // Check if it has a data array with message
        if ($jsonData && isset($jsonData['data']) && is_array($jsonData['data']) && isset($jsonData['data'][0]['message'])) {
            $message = $jsonData['data'][0]['message'];
            return parseTextResponse($message);
        }
        
        // Check if it has a reply field
        if ($jsonData && isset($jsonData['reply'])) {
            return parseTextResponse($jsonData['reply']);
        }
        
        // Parse as text
        return parseTextResponse($responseText);
        
    } catch (Exception $e) {
        error_log('Error parsing test response: ' . $e->getMessage());
        return ['success' => false];
    }
}

function parseTextResponse($text) {
    // Extract username - try multiple patterns
    $username = null;
    if (preg_match('/✅\s*\*?Usuário\*?:\s*(\d+)/i', $text, $matches)) {
        $username = $matches[1];
    } elseif (preg_match('/Usuario:\s*(\d+)/i', $text, $matches)) {
        $username = $matches[1];
    } elseif (preg_match('/User:\s*(\d+)/i', $text, $matches)) {
        $username = $matches[1];
    } elseif (preg_match('/username[:\s]*(\d+)/i', $text, $matches)) {
        $username = $matches[1];
    }
    
    // Extract password - try multiple patterns
    $password = null;
    if (preg_match('/✅\s*\*?Senha\*?:\s*(\d+)/i', $text, $matches)) {
        $password = $matches[1];
    } elseif (preg_match('/Senha:\s*(\d+)/i', $text, $matches)) {
        $password = $matches[1];
    } elseif (preg_match('/Password:\s*(\d+)/i', $text, $matches)) {
        $password = $matches[1];
    } elseif (preg_match('/password[:\s]*(\d+)/i', $text, $matches)) {
        $password = $matches[1];
    }
    
    // Check if we have at least username and password
    if (!$username || !$password) {
        error_log('Failed to extract username/password from response');
        return ['success' => false];
    }
    
    return [
        'success' => true,
        'username' => $username,
        'password' => $password,
        'code' => extractCodeFromReply($text),
        'dnsStb' => extractDnsStb($text),
        'urlXciptv' => extractXciptvUrls($text),
        'linkM3u' => extractM3uLink($text),
        'linkM3uShort' => extractM3uShortLink($text),
        'linkHls' => extractHlsLink($text),
        'linkHlsShort' => extractHlsShortLink($text),
        'linkSsiptv' => extractSsiptvLink($text),
        'webPlayers' => extractWebPlayers($text),
        'iptvStream' => extractIptvStream($text),
        'expiresAt' => extractExpiresAt($text),
        'connections' => extractConnections($text),
        'planName' => extractPlanName($text),
        'price' => extractPrice($text),
        'createdAt' => extractCreatedAt($text),
        'renewalUrl' => extractRenewalUrl($text)
    ];
}

function extractCodeFromReply($text) {
    if (preg_match('/📌\s*\*?CODE\s*\*?\s*:\s*(\d+)/i', $text, $matches)) {
        return $matches[1];
    } elseif (preg_match('/CODE[:\s]*(\d+)/i', $text, $matches)) {
        return $matches[1];
    }
    return null;
}

function extractDnsFromUrl($url) {
    if (preg_match('/https?:\/\/([^:\/\s]+)/', $url, $matches)) {
        return $matches[1];
    }
    return null;
}

function extractDnsStb($text) {
    if (preg_match('/📺\s*\*?DNS\s*STB[\/\\\\]?SmartUp:?V?3?\*?\s*([\d.]+)/i', $text, $matches)) {
        return $matches[1];
    } elseif (preg_match('/DNS[:\s]*([\d.]+)/i', $text, $matches)) {
        return $matches[1];
    }
    return null;
}

function extractXciptvUrls($text) {
    $urls = [];
    if (preg_match_all('/🟠\s*\*?URL\s*XCIPTV\*?:\s*(http[s]?:\/\/[^\s\n*]+)/i', $text, $matches)) {
        foreach ($matches[1] as $url) {
            $urls[] = str_replace('*', '', $url);
        }
    }
    return $urls;
}

function extractM3uLink($text) {
    if (preg_match('/🟢\s*\*?Link\s*\(M3U\)\*?:\s*(http[s]?:\/\/[^\s\n*]+)/i', $text, $matches)) {
        return str_replace('*', '', $matches[1]);
    } elseif (preg_match('/M3U[:\s]*(http[s]?:\/\/[^\s\n*]+)/i', $text, $matches)) {
        return str_replace('*', '', $matches[1]);
    }
    return null;
}

function extractM3uShortLink($text) {
    if (preg_match('/🟢\s*\*?Link\s*Curto\s*\(M3U\)\*?:\s*(http[s]?:\/\/[^\s\n*]+)/i', $text, $matches)) {
        return str_replace('*', '', $matches[1]);
    } elseif (preg_match('/Link\s*Curto.*M3U[:\s]*(http[s]?:\/\/[^\s\n*]+)/i', $text, $matches)) {
        return str_replace('*', '', $matches[1]);
    }
    return null;
}

function extractHlsLink($text) {
    if (preg_match('/🟡\s*\*?Link\s*\(HLS\)\*?:\s*(http[s]?:\/\/[^\s\n*]+)/i', $text, $matches)) {
        return str_replace('*', '', $matches[1]);
    } elseif (preg_match('/HLS[:\s]*(http[s]?:\/\/[^\s\n*]+)/i', $text, $matches)) {
        return str_replace('*', '', $matches[1]);
    }
    return null;
}

function extractHlsShortLink($text) {
    if (preg_match('/🟡\s*\*?Link\s*Curto\s*\(HLS\)\*?:\s*(http[s]?:\/\/[^\s\n*]+)/i', $text, $matches)) {
        return str_replace('*', '', $matches[1]);
    } elseif (preg_match('/Link\s*Curto.*HLS[:\s]*(http[s]?:\/\/[^\s\n*]+)/i', $text, $matches)) {
        return str_replace('*', '', $matches[1]);
    }
    return null;
}

function extractSsiptvLink($text) {
    if (preg_match('/🔴\s*\*?Link\s*\(SSIPTV\)\*?:\s*(http[s]?:\/\/[^\s\n*]+)/i', $text, $matches)) {
        return str_replace('*', '', $matches[1]);
    } elseif (preg_match('/SSIPTV[:\s]*(http[s]?:\/\/[^\s\n*]+)/i', $text, $matches)) {
        return str_replace('*', '', $matches[1]);
    }
    return null;
}

function extractWebPlayers($text) {
    if (preg_match('/📺\s*\*?WEB\s*PLAYER\*?:\s*((?:http[s]?:\/\/[^\s\n*]+\s*)+)/i', $text, $matches)) {
        $urls = preg_split('/\s+/', trim($matches[1]));
        return array_filter(array_map(function($url) {
            return strpos($url, 'http') === 0 ? str_replace('*', '', $url) : null;
        }, $urls));
    }
    return [];
}

function extractIptvStream($text) {
    if (preg_match('/📺\s*\*?IPTV\s*STREAM\*?\s*(https?:\/\/[^\s\n*]+)/i', $text, $matches)) {
        return str_replace('*', '', $matches[1]);
    } elseif (preg_match('/IPTV\s*STREAM[:\s]*(https?:\/\/[^\s\n*]+)/i', $text, $matches)) {
        return str_replace('*', '', $matches[1]);
    }
    return null;
}

function extractExpiresAt($text) {
    if (preg_match('/🗓️\s*\*?Vencimento\*?:\s*([^\n\r*]+)/i', $text, $matches)) {
        return str_replace('*', '', trim($matches[1]));
    } elseif (preg_match('/Vencimento[:\s]*([^\n\r*]+)/i', $text, $matches)) {
        return str_replace('*', '', trim($matches[1]));
    } elseif (preg_match('/Expira[:\s]*([^\n\r*]+)/i', $text, $matches)) {
        return str_replace('*', '', trim($matches[1]));
    }
    return null;
}

function extractConnections($text) {
    if (preg_match('/📶\s*\*?Conexões\*?:\s*(\d+)/i', $text, $matches)) {
        return intval($matches[1]);
    } elseif (preg_match('/Conexões[:\s]*(\d+)/i', $text, $matches)) {
        return intval($matches[1]);
    } elseif (preg_match('/Connections[:\s]*(\d+)/i', $text, $matches)) {
        return intval($matches[1]);
    }
    return null;
}

function extractPlanName($text) {
    if (preg_match('/📦\s*\*?Plano\*?:\s*([^\n\r*]+)/i', $text, $matches)) {
        return str_replace('*', '', trim($matches[1]));
    } elseif (preg_match('/Plano[:\s]*([^\n\r*]+)/i', $text, $matches)) {
        return str_replace('*', '', trim($matches[1]));
    } elseif (preg_match('/Plan[:\s]*([^\n\r*]+)/i', $text, $matches)) {
        return str_replace('*', '', trim($matches[1]));
    }
    return null;
}

function extractPrice($text) {
    if (preg_match('/💵\s*\*?Preço\s*do\s*Plano\*?:\s*([^\n\r*]+)/i', $text, $matches)) {
        return str_replace('*', '', trim($matches[1]));
    } elseif (preg_match('/Preço[:\s]*([^\n\r*]+)/i', $text, $matches)) {
        return str_replace('*', '', trim($matches[1]));
    } elseif (preg_match('/Price[:\s]*([^\n\r*]+)/i', $text, $matches)) {
        return str_replace('*', '', trim($matches[1]));
    }
    return null;
}

function extractCreatedAt($text) {
    if (preg_match('/🗓️\s*\*?Criado\s*em\*?:\s*([^\n\r*]+)/i', $text, $matches)) {
        return str_replace('*', '', trim($matches[1]));
    } elseif (preg_match('/Criado[:\s]*([^\n\r*]+)/i', $text, $matches)) {
        return str_replace('*', '', trim($matches[1]));
    } elseif (preg_match('/Created[:\s]*([^\n\r*]+)/i', $text, $matches)) {
        return str_replace('*', '', trim($matches[1]));
    }
    return null;
}

function extractRenewalUrl($text) {
    if (preg_match('/💳\s*\*?Assinar[\/\\\\]?Renovar\s*Plano\*?:\s*(https?:\/\/[^\s\n*]+)/i', $text, $matches)) {
        return str_replace('*', '', $matches[1]);
    } elseif (preg_match('/Renovar[:\s]*(https?:\/\/[^\s\n*]+)/i', $text, $matches)) {
        return str_replace('*', '', $matches[1]);
    } elseif (preg_match('/Renewal[:\s]*(https?:\/\/[^\s\n*]+)/i', $text, $matches)) {
        return str_replace('*', '', $matches[1]);
    }
    return null;
}
?>
